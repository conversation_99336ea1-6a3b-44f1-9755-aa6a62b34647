# 角色权限控制实现指南

## 概述

本项目已实现基于角色的页面访问控制，确保只有 `admin` 或 `super_admin` 角色的用户才能访问后台管理页面，其他用户只能访问前台页面。

## 实现功能

### 1. 角色权限控制
- **管理员角色**: `admin` 和 `super_admin` 可以访问所有页面（前台 + 后台）
- **普通用户**: 其他角色只能访问前台页面和白名单页面
- **未登录用户**: 只能访问白名单页面

### 2. 页面分类

#### 前台页面（无需管理员权限）
- `/frontend` - 数据查询平台主页
- `/frontend-test` - 前台测试页面  
- `/frontend-demo` - 前台演示页面

#### 后台页面（需要管理员权限）
- `/` - 后台首页
- `/system/*` - 系统管理相关页面
- `/infra/*` - 基础设施管理页面
- 其他所有非前台和白名单的页面

#### 白名单页面（无需登录）
- `/login` - 登录页面
- `/social-login` - 社交登录
- `/auth-redirect` - 认证重定向
- `/bind` - 绑定页面
- `/register` - 注册页面
- `/oauthLogin/gitee` - Gitee OAuth登录
- 前台页面（见上）
- `/role-permission-test` - 权限测试页面（仅用于开发测试）

## 核心文件修改

### 1. `src/utils/permission.ts`
新增 `checkAdminRole()` 函数，专门用于检查用户是否具有后台管理权限：

```typescript
export function checkAdminRole() {
  const { wsCache } = useCache()
  const userInfo = wsCache.get(CACHE_KEY.USER)
  const roles = userInfo?.roles || []
  
  return roles.some((role: string) => {
    return role === 'admin' || role === 'super_admin'
  })
}
```

### 2. `src/permission.ts`
修改路由守卫逻辑，添加角色检查：

- 导入 `checkAdminRole` 函数
- 添加前台页面路径检查函数 `isFrontendPage()`
- 在路由守卫中添加角色权限验证逻辑
- 权限不足时跳转到 403 页面而不是登录页面

### 3. `src/views/Error/403.vue`
优化 403 页面显示：

- 明确说明权限不足的原因
- 提供前往前台页面和返回首页的按钮
- 更友好的用户体验

## 权限控制流程

```mermaid
flowchart TD
    A[用户访问页面] --> B{是否有Token?}
    B -->|否| C{是否在白名单?}
    C -->|是| D[允许访问]
    C -->|否| E[跳转登录页面]
    
    B -->|是| F{用户信息是否已设置?}
    F -->|否| G[获取用户信息并设置路由]
    F -->|是| H{是否为前台页面或白名单?}
    
    G --> H
    H -->|是| D
    H -->|否| I{是否有管理员权限?}
    
    I -->|是| D
    I -->|否| J[跳转403页面]
```

## 测试功能

### 测试页面
访问 `/role-permission-test` 可以进行权限测试，该页面提供：

- 查看当前用户角色和权限状态
- 模拟不同角色用户
- 运行自动化权限测试
- 测试页面访问权限

### 测试工具
`src/utils/roleTestHelper.ts` 提供了以下测试函数：

- `mockUserRoles(roles)` - 模拟设置用户角色
- `testRolePermissions()` - 运行权限测试
- `getCurrentUserRoles()` - 获取当前用户角色信息
- `clearUserCache()` - 清除用户缓存

## 使用示例

### 在组件中检查权限
```typescript
import { checkAdminRole } from '@/utils/permission'

// 检查是否有管理员权限
if (checkAdminRole()) {
  // 显示管理员功能
} else {
  // 隐藏或禁用管理员功能
}
```

### 在模板中使用权限指令
```vue
<template>
  <!-- 只有admin或super_admin角色才能看到 -->
  <el-button v-hasRole="['admin', 'super_admin']">
    管理员操作
  </el-button>
</template>
```

## 注意事项

1. **开发环境**: 测试页面 `/role-permission-test` 仅用于开发测试，生产环境应移除
2. **角色名称**: 确保后端返回的角色名称与前端检查的角色名称一致
3. **缓存清理**: 角色变更后需要重新登录或清除缓存才能生效
4. **路由配置**: 新增的后台页面会自动受到权限控制，无需额外配置

## 安全建议

1. 前端权限控制主要用于用户体验，真正的安全控制应在后端实现
2. 敏感操作应在后端进行二次权限验证
3. 定期检查和更新权限配置
4. 监控异常的权限访问尝试
